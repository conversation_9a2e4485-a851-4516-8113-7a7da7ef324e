#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON文件拼接脚本
功能：将不同的JSON文件按照"案号"字段进行拼接合并
如果除了"案号"以外还有相同字段，会报告错误
"""

import json
import os
import sys
from typing import Dict, List, Any, Set
from collections import defaultdict


class JSONMerger:
    def __init__(self, primary_key: str = "案号"):
        """
        初始化JSON合并器

        Args:
            primary_key: 主索引字段名，默认为"案号"
        """
        self.primary_key = primary_key
        self.merged_data = {}
        self.field_conflicts = defaultdict(list)  # 记录字段冲突
        self.file_fields = {}  # 记录每个文件的字段

    def load_json_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        加载JSON文件

        Args:
            file_path: JSON文件路径

        Returns:
            JSON数据列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 如果是单个对象，转换为列表
            if isinstance(data, dict):
                data = [data]
            elif not isinstance(data, list):
                raise ValueError(f"文件 {file_path} 的JSON格式不正确，应为对象或对象数组")

            return data
        except FileNotFoundError:
            print(f"错误：文件 {file_path} 不存在")
            return []
        except json.JSONDecodeError as e:
            print(f"错误：文件 {file_path} JSON格式错误: {e}")
            return []
        except Exception as e:
            print(f"错误：读取文件 {file_path} 时发生错误: {e}")
            return []

    def get_fields_from_data(self, data: List[Dict[str, Any]]) -> Set[str]:
        """
        获取数据中所有的字段名

        Args:
            data: JSON数据列表

        Returns:
            字段名集合
        """
        fields = set()
        for item in data:
            if isinstance(item, dict):
                fields.update(item.keys())
        return fields

    def check_field_conflicts(self, file_path: str, fields: Set[str]) -> List[str]:
        """
        检查字段冲突

        Args:
            file_path: 文件路径
            fields: 当前文件的字段集合

        Returns:
            冲突字段列表
        """
        conflicts = []

        for existing_file, existing_fields in self.file_fields.items():
            # 找出除主索引外的重复字段
            common_fields = fields.intersection(existing_fields)
            common_fields.discard(self.primary_key)  # 排除主索引字段

            if common_fields:
                conflicts.extend(common_fields)
                for field in common_fields:
                    self.field_conflicts[field].extend([existing_file, file_path])

        return list(set(conflicts))  # 去重

    def merge_single_file(self, file_path: str) -> bool:
        """
        合并单个JSON文件

        Args:
            file_path: JSON文件路径

        Returns:
            是否成功合并
        """
        print(f"正在处理文件: {file_path}")

        # 加载数据
        data = self.load_json_file(file_path)
        if not data:
            return False

        # 获取字段信息
        fields = self.get_fields_from_data(data)

        # 检查是否包含主索引字段
        if self.primary_key not in fields:
            print(f"错误：文件 {file_path} 中没有找到主索引字段 '{self.primary_key}'")
            return False

        # 检查字段冲突
        conflicts = self.check_field_conflicts(file_path, fields)
        if conflicts:
            print(f"错误：文件 {file_path} 与已处理文件存在字段冲突:")
            for field in conflicts:
                conflicting_files = list(set(self.field_conflicts[field]))
                print(f"  字段 '{field}' 在以下文件中重复: {', '.join(conflicting_files)}")
            return False

        # 记录文件字段
        self.file_fields[file_path] = fields

        # 合并数据
        merged_count = 0
        for item in data:
            if not isinstance(item, dict):
                continue

            primary_value = item.get(self.primary_key)
            if primary_value is None:
                print(f"警告：文件 {file_path} 中发现空的主索引值，跳过该条记录")
                continue

            # 转换主索引为字符串以确保一致性
            primary_value = str(primary_value)

            if primary_value in self.merged_data:
                # 合并到现有记录
                self.merged_data[primary_value].update(item)
            else:
                # 创建新记录
                self.merged_data[primary_value] = item.copy()

            merged_count += 1

        print(f"成功合并 {merged_count} 条记录")
        return True

    def merge_files(self, file_paths: List[str]) -> bool:
        """
        合并多个JSON文件

        Args:
            file_paths: JSON文件路径列表

        Returns:
            是否全部成功合并
        """
        print(f"开始合并 {len(file_paths)} 个JSON文件...")
        print(f"主索引字段: {self.primary_key}")
        print("-" * 50)

        success_count = 0
        for file_path in file_paths:
            if self.merge_single_file(file_path):
                success_count += 1
            print("-" * 30)

        print(f"合并完成: {success_count}/{len(file_paths)} 个文件成功处理")
        print(f"总共合并了 {len(self.merged_data)} 条唯一记录")

        return success_count == len(file_paths)

    def save_merged_data(self, output_path: str, indent: int = 2) -> bool:
        """
        保存合并后的数据

        Args:
            output_path: 输出文件路径
            indent: JSON缩进

        Returns:
            是否保存成功
        """
        try:
            # 转换为列表格式
            output_data = list(self.merged_data.values())

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=indent)

            print(f"合并结果已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"错误：保存文件时发生错误: {e}")
            return False

    def print_summary(self):
        """打印合并摘要"""
        print("\n" + "=" * 50)
        print("合并摘要:")
        print(f"主索引字段: {self.primary_key}")
        print(f"处理文件数: {len(self.file_fields)}")
        print(f"合并记录数: {len(self.merged_data)}")

        if self.file_fields:
            print("\n各文件字段信息:")
            for file_path, fields in self.file_fields.items():
                print(f"  {os.path.basename(file_path)}: {len(fields)} 个字段")
                print(f"    字段: {', '.join(sorted(fields))}")

        if self.field_conflicts:
            print(f"\n检测到 {len(self.field_conflicts)} 个字段冲突:")
            for field, files in self.field_conflicts.items():
                print(f"  字段 '{field}' 在文件中重复: {', '.join(set(files))}")


def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python merge_json.py <输出文件> <输入文件1> [输入文件2] ...")
        print("示例: python merge_json.py merged_output.json file1.json file2.json file3.json")
        print("可选参数:")
        print("  --primary-key <字段名>  指定主索引字段名（默认为'案号'）")
        sys.exit(1)

    # 解析命令行参数
    args = sys.argv[1:]
    primary_key = "案号"

    # 检查是否指定了主索引字段
    if "--primary-key" in args:
        key_index = args.index("--primary-key")
        if key_index + 1 < len(args):
            primary_key = args[key_index + 1]
            # 移除这两个参数
            args.pop(key_index)  # 移除 --primary-key
            args.pop(key_index)  # 移除字段名
        else:
            print("错误: --primary-key 参数需要指定字段名")
            sys.exit(1)

    if len(args) < 2:
        print("错误: 至少需要指定输出文件和一个输入文件")
        sys.exit(1)

    output_file = args[0]
    input_files = args[1:]

    # 检查输入文件是否存在
    missing_files = [f for f in input_files if not os.path.exists(f)]
    if missing_files:
        print(f"错误: 以下文件不存在: {', '.join(missing_files)}")
        sys.exit(1)

    # 创建合并器并执行合并
    merger = JSONMerger(primary_key=primary_key)

    success = merger.merge_files(input_files)

    if success and merger.merged_data:
        merger.save_merged_data(output_file)
        merger.print_summary()
    else:
        print("合并失败或没有数据可合并")
        sys.exit(1)


if __name__ == "__main__":
    main()