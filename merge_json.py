#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import sys
import os
from typing import Dict, List, Any
from pathlib import Path

def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        # 如果不是列表格式，转换为列表
        if not isinstance(data, list):
            data = [data]
        return data
    except Exception as e:
        print(f"错误：无法读取文件 {file_path}: {e}")
        return []

def merge_json_files(file_paths: List[str], output_path: str = None) -> Dict[str, Any]:
    """
    合并多个JSON文件，以"案号"为主索引
    """
    merged_data = {}
    field_conflicts = []
    
    for file_path in file_paths:
        print(f"处理文件: {file_path}")
        data = load_json_file(file_path)
        
        if not data:
            continue
            
        for record in data:
            if "案号" not in record:
                print(f"警告：在文件 {file_path} 中发现没有'案号'字段的记录，跳过")
                continue
                
            case_number = record["案号"]
            
            if case_number not in merged_data:
                # 新案号，直接添加
                merged_data[case_number] = record.copy()
                merged_data[case_number]["_source_files"] = [file_path]
            else:
                # 案号已存在，检查字段冲突
                existing_record = merged_data[case_number]
                conflicts = []
                
                for key, value in record.items():
                    if key == "案号":
                        continue
                        
                    if key in existing_record:
                        if existing_record[key] != value:
                            conflicts.append({
                                "案号": case_number,
                                "字段": key,
                                "文件1": existing_record.get("_source_files", ["未知"])[0],
                                "值1": existing_record[key],
                                "文件2": file_path,
                                "值2": value
                            })
                        # 相同字段相同值，不处理
                    else:
                        # 新字段，添加到记录中
                        existing_record[key] = value
                
                # 添加源文件信息
                if "_source_files" not in existing_record:
                    existing_record["_source_files"] = []
                existing_record["_source_files"].append(file_path)
                
                if conflicts:
                    field_conflicts.extend(conflicts)
    
    # 报告冲突
    if field_conflicts:
        print("\n=== 发现字段冲突 ===")
        for conflict in field_conflicts:
            print(f"案号: {conflict['案号']}")
            print(f"冲突字段: {conflict['字段']}")
            print(f"文件1 ({conflict['文件1']}): {conflict['值1']}")
            print(f"文件2 ({conflict['文件2']}): {conflict['值2']}")
            print("-" * 50)
        
        print(f"\n总共发现 {len(field_conflicts)} 个字段冲突")
        return None
    
    # 清理源文件信息（可选）
    for record in merged_data.values():
        if "_source_files" in record:
            del record["_source_files"]
    
    # 转换为列表格式
    result = list(merged_data.values())
    
    # 保存结果
    if output_path:
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n合并结果已保存到: {output_path}")
        except Exception as e:
            print(f"错误：无法保存文件 {output_path}: {e}")
    
    return result

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python merge_json.py <json文件1> <json文件2> [json文件3...] [输出文件]")
        print("示例: python merge_json.py file1.json file2.json file3.json merged.json")
        sys.exit(1)
    
    # 解析命令行参数
    file_paths = sys.argv[1:-1]
    output_path = sys.argv[-1]
    
    # 检查是否最后一个参数是输出文件
    if not output_path.endswith('.json'):
        file_paths.append(output_path)
        output_path = "merged_result.json"
    
    # 验证输入文件
    valid_files = []
    for file_path in file_paths:
        if os.path.exists(file_path):
            valid_files.append(file_path)
        else:
            print(f"警告：文件不存在，跳过: {file_path}")
    
    if len(valid_files) < 2:
        print("错误：至少需要两个有效的JSON文件进行合并")
        sys.exit(1)
    
    print(f"准备合并 {len(valid_files)} 个文件...")
    
    # 执行合并
    result = merge_json_files(valid_files, output_path)
    
    if result is not None:
        print(f"\n合并完成！共处理 {len(result)} 条记录")
    else:
        print("\n合并失败：存在字段冲突，请检查数据后重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
